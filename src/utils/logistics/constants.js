/**
 * 物流状态分析器 - 常量定义
 * 包含物流状态枚举、文本映射等核心常量
 */

// 物流状态枚举
export const LOGISTICS_STATUS = {
  PENDING_PICKUP: 'pending_pickup',     // 待揽件
  PICKED_UP: 'picked_up',               // 已揽件
  IN_TRANSIT: 'in_transit',             // 运输中
  OUT_FOR_DELIVERY: 'out_for_delivery', // 派送中
  DELIVERED: 'delivered',               // 已签收
  FAILED_DELIVERY: 'failed_delivery',   // 派送失败
  RETURNED: 'returned',                 // 已退回
  EXCEPTION: 'exception'                // 异常
}

// 状态显示文本映射
export const STATUS_TEXT_MAP = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: '待揽件',
  [LOGISTICS_STATUS.PICKED_UP]: '已揽收',
  [LOGISTICS_STATUS.IN_TRANSIT]: '运输中',
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中',
  [LOGISTICS_STATUS.DELIVERED]: '已签收',
  [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败',
  [LOGISTICS_STATUS.RETURNED]: '已退回',
  [LOGISTICS_STATUS.EXCEPTION]: '异常'
}

// 状态优先级映射（数值越高优先级越高）
export const STATUS_PRIORITY = {
  [LOGISTICS_STATUS.DELIVERED]: 100,
  [LOGISTICS_STATUS.EXCEPTION]: 90,
  [LOGISTICS_STATUS.FAILED_DELIVERY]: 80,
  [LOGISTICS_STATUS.RETURNED]: 75,
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 60,
  [LOGISTICS_STATUS.IN_TRANSIT]: 40,
  [LOGISTICS_STATUS.PICKED_UP]: 30,
  [LOGISTICS_STATUS.PENDING_PICKUP]: 10
}

// 终态状态集合
export const FINAL_STATUSES = new Set([
  LOGISTICS_STATUS.DELIVERED,
  LOGISTICS_STATUS.RETURNED,
  LOGISTICS_STATUS.EXCEPTION
])

// 状态紧急程度映射 (1-5, 5最紧急)
export const STATUS_URGENCY = {
  [LOGISTICS_STATUS.EXCEPTION]: 5,
  [LOGISTICS_STATUS.FAILED_DELIVERY]: 4,
  [LOGISTICS_STATUS.RETURNED]: 4,
  [LOGISTICS_STATUS.PENDING_PICKUP]: 3,
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 3,
  [LOGISTICS_STATUS.IN_TRANSIT]: 2,
  [LOGISTICS_STATUS.PICKED_UP]: 2,
  [LOGISTICS_STATUS.DELIVERED]: 1
}

// 状态转换规则映射
export const VALID_TRANSITIONS = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: [
    LOGISTICS_STATUS.PICKED_UP, 
    LOGISTICS_STATUS.EXCEPTION
  ],
  [LOGISTICS_STATUS.PICKED_UP]: [
    LOGISTICS_STATUS.IN_TRANSIT, 
    LOGISTICS_STATUS.OUT_FOR_DELIVERY, 
    LOGISTICS_STATUS.EXCEPTION
  ],
  [LOGISTICS_STATUS.IN_TRANSIT]: [
    LOGISTICS_STATUS.OUT_FOR_DELIVERY, 
    LOGISTICS_STATUS.DELIVERED, 
    LOGISTICS_STATUS.EXCEPTION
  ],
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: [
    LOGISTICS_STATUS.DELIVERED, 
    LOGISTICS_STATUS.FAILED_DELIVERY, 
    LOGISTICS_STATUS.EXCEPTION
  ],
  [LOGISTICS_STATUS.DELIVERED]: [], // 终态
  [LOGISTICS_STATUS.FAILED_DELIVERY]: [
    LOGISTICS_STATUS.OUT_FOR_DELIVERY, 
    LOGISTICS_STATUS.RETURNED, 
    LOGISTICS_STATUS.EXCEPTION
  ],
  [LOGISTICS_STATUS.RETURNED]: [], // 终态
  [LOGISTICS_STATUS.EXCEPTION]: [
    LOGISTICS_STATUS.IN_TRANSIT, 
    LOGISTICS_STATUS.OUT_FOR_DELIVERY, 
    LOGISTICS_STATUS.RETURNED
  ]
}

// 分析配置常量
export const ANALYSIS_CONFIG = {
  // 置信度阈值
  HIGH_CONFIDENCE_THRESHOLD: 0.8,
  MEDIUM_CONFIDENCE_THRESHOLD: 0.5,
  LOW_CONFIDENCE_THRESHOLD: 0.3,
  
  // 权重配置
  KEYWORD_WEIGHTS: {
    high: 30,
    medium: 15,
    low: 8
  },
  
  // 时间因素配置
  TIME_BONUS: {
    RECENT_HOURS: 2,      // 2小时内
    DAILY_HOURS: 24,      // 24小时内
    WEEKLY_HOURS: 168     // 一周内
  },
  
  // 语义分析配置
  SEMANTIC_BONUS_LIMIT: 10,
  COHERENCE_BONUS_LIMIT: 5,
  
  // 性能配置
  MAX_TRACK_ANALYSIS: 5,  // 最多分析5条轨迹
  CACHE_EXPIRE_TIME: 300000, // 缓存过期时间 5分钟
  
  // AI模型配置
  AI_MODEL_TIMEOUT: 3000,  // AI模型超时时间 3秒
  AI_FALLBACK_ENABLED: true
}

// 模糊上下文关键词
export const AMBIGUOUS_KEYWORDS = [
  '处理中', '处理', '操作中', '进行中', '执行中',
  '快件', '包裹', '订单', '货物', '商品',
  '正在', '进行', '执行', '操作', '系统',
  '信息', '数据', '记录', '更新'
]

// 情感分析词汇
export const SENTIMENT_WORDS = {
  positive: [
    '成功', '完成', '正常', '顺利', '妥投', 
    '及时', '准时', '快速', '安全', '满意',
    '良好', '优质', '高效', '专业', '贴心'
  ],
  negative: [
    '失败', '错误', '延误', '异常', '问题',
    '困难', '阻碍', '故障', '损坏', '丢失',
    '超时', '拒绝', '无法', '不能', '未能'
  ]
}

// 时间逻辑词汇
export const TIME_LOGIC_WORDS = [
  '然后', '接着', '随后', '之后', '现在', 
  '目前', '刚刚', '即将', '马上', '立即',
  '稍后', '不久', '很快', '正在', '已经'
]

// 因果关系词汇
export const CAUSAL_WORDS = [
  '因为', '由于', '所以', '因此', '导致',
  '造成', '引起', '产生', '形成', '带来'
]

// 地点连续性词汇
export const LOCATION_WORDS = [
  '从', '到', '经过', '途径', '抵达',
  '离开', '前往', '运往', '送往', '转至'
]

/**
 * 判断是否为终态状态
 * @param {string} status - 物流状态
 * @returns {boolean} 是否为终态
 */
export function isFinalStatus(status) {
  return FINAL_STATUSES.has(status)
}

/**
 * 获取状态的紧急程度
 * @param {string} status - 物流状态
 * @returns {number} 紧急程度 (1-5, 5最紧急)
 */
export function getStatusUrgency(status) {
  return STATUS_URGENCY[status] || 2
}

/**
 * 获取状态优先级
 * @param {string} status - 物流状态
 * @returns {number} 优先级数值
 */
export function getStatusPriority(status) {
  return STATUS_PRIORITY[status] || 0
}

/**
 * 获取可能的下一状态
 * @param {string} currentStatus - 当前状态
 * @returns {Array} 可能的下一状态数组
 */
export function getPossibleNextStates(currentStatus) {
  return VALID_TRANSITIONS[currentStatus] || []
}

/**
 * 验证状态转换是否合理
 * @param {string} fromStatus - 源状态
 * @param {string} toStatus - 目标状态
 * @returns {boolean} 转换是否合理
 */
export function isValidTransition(fromStatus, toStatus) {
  if (fromStatus === toStatus) return true
  const allowedStates = getPossibleNextStates(fromStatus)
  return allowedStates.includes(toStatus)
}
