/**
 * 物流状态分析器测试文件
 * 用于验证新模块的功能是否正常
 */

import { quickAnalyze, createAnalyzer, LOGISTICS_STATUS } from './index.js'

// 测试数据
const testTrackData = [
  {
    context: '您的快件已签收，感谢使用顺丰速运',
    msgTime: '2024-01-01 10:00:00'
  },
  {
    context: '快件正在派送中，派送员：张师傅',
    msgTime: '2024-01-01 09:00:00'
  },
  {
    context: '快件已到达北京分拣中心',
    msgTime: '2024-01-01 08:00:00'
  }
]

/**
 * 测试快速分析功能
 */
async function testQuickAnalyze() {
  console.log('=== 测试快速分析功能 ===')
  
  try {
    const result = await quickAnalyze(testTrackData)
    console.log('分析结果:', {
      status: result.status,
      statusText: result.statusText,
      confidence: result.confidence,
      analysisMethod: result.analysisMethod
    })
    
    // 验证结果
    if (result.status === LOGISTICS_STATUS.DELIVERED) {
      console.log('✅ 快速分析测试通过 - 正确识别为已签收状态')
    } else {
      console.log('❌ 快速分析测试失败 - 状态识别错误')
    }
  } catch (error) {
    console.error('❌ 快速分析测试失败:', error.message)
  }
}

/**
 * 测试分析器实例
 */
async function testAnalyzerInstance() {
  console.log('\n=== 测试分析器实例 ===')
  
  try {
    const analyzer = createAnalyzer({
      enableAI: false, // 关闭AI避免依赖问题
      enableCache: true,
      enableErrorHandling: true
    })
    
    const result = await analyzer.analyze(testTrackData)
    console.log('分析器实例结果:', {
      status: result.status,
      statusText: result.statusText,
      confidence: result.confidence
    })
    
    // 获取详细分析
    const detailedResult = await analyzer.getDetailedAnalysis(testTrackData)
    console.log('详细分析结果:', {
      urgency: detailedResult.urgency,
      isFinal: detailedResult.isFinal,
      recommendations: detailedResult.recommendations
    })
    
    console.log('✅ 分析器实例测试通过')
  } catch (error) {
    console.error('❌ 分析器实例测试失败:', error.message)
  }
}

/**
 * 测试批量分析
 */
async function testBatchAnalyze() {
  console.log('\n=== 测试批量分析 ===')
  
  try {
    const analyzer = createAnalyzer({
      enableAI: false,
      enableCache: true
    })
    
    const batchData = [
      testTrackData,
      [
        {
          context: '快件派送失败，收件人不在',
          msgTime: '2024-01-01 15:00:00'
        }
      ],
      [
        {
          context: '快件正在运输途中',
          msgTime: '2024-01-01 12:00:00'
        }
      ]
    ]
    
    const results = await analyzer.batchAnalyze(batchData)
    console.log('批量分析结果:')
    results.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.statusText} (置信度: ${result.confidence.toFixed(2)})`)
    })
    
    console.log('✅ 批量分析测试通过')
  } catch (error) {
    console.error('❌ 批量分析测试失败:', error.message)
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===')
  
  try {
    // 测试空数据
    const result1 = await quickAnalyze([])
    console.log('空数据处理结果:', result1.status)
    
    // 测试无效数据
    const result2 = await quickAnalyze(null)
    console.log('无效数据处理结果:', result2.status)
    
    console.log('✅ 错误处理测试通过')
  } catch (error) {
    console.error('❌ 错误处理测试失败:', error.message)
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行物流状态分析器测试...\n')
  
  await testQuickAnalyze()
  await testAnalyzerInstance()
  await testBatchAnalyze()
  await testErrorHandling()
  
  console.log('\n🎉 所有测试完成!')
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runAllTests().catch(console.error)
} else {
  // 浏览器环境，导出测试函数
  window.logisticsAnalyzerTest = {
    runAllTests,
    testQuickAnalyze,
    testAnalyzerInstance,
    testBatchAnalyze,
    testErrorHandling
  }
}

export {
  runAllTests,
  testQuickAnalyze,
  testAnalyzerInstance,
  testBatchAnalyze,
  testErrorHandling
}
