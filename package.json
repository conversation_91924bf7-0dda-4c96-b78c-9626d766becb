{"name": "ps-ccms-biz-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "cross-env EPAYENV=development vite --mode development", "sit": "cross-env EPAYENV=xhm-k8s-sit vite build --mode sit", "uat": "cross-env EPAYENV=xhm-k8s-uat vite build --mode uat", "build": "node scripts/build.js", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@alova/adapter-axios": "^2.0.14", "@tensorflow/tfjs": "^4.22.0", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^13.3.0", "@xenova/transformers": "^2.17.2", "alova": "^3.2.13", "axios": "^1.9.0", "commonkit": "^10.0.0-alpha.1", "commonkit-login-vue3": "^10.0.0-alpha.1", "compromise": "^14.14.4", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "intersection-observer-polyfill": "^0.1.0", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "qs": "^6.14.0", "regenerator-runtime": "^0.14.1", "reset.css": "^2.0.2", "resize-observer-polyfill": "^1.5.1", "swiper": "^11.2.10", "url-parse": "^1.5.10", "vant": "^4.9.19", "video.js": "^7.21.7", "videojs-hotkeys": "^0.2.30", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-router": "^4.5.0", "vue-waterfall-plugin-next": "^2.6.5", "vue-wechat-title": "^2.0.7", "wo-e2": "^1.2.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "less": "^4.3.0", "postcss": "^8.5.4", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-html": "^3.2.2", "vite-plugin-style-import": "^2.0.0", "vite-plugin-vue-devtools": "^7.7.2"}}