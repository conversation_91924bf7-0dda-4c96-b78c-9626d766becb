/**
 * 物流状态分析器 - AI增强分析模块
 * 集成前端AI模型进行语义分析，支持多种AI库和兜底机制
 */

import { LOGISTICS_STATUS, ANALYSIS_CONFIG } from './constants.js'
import { SENTIMENT_WORDS } from './constants.js'

// AI模型状态管理
class AIModelManager {
  constructor() {
    this.models = new Map()
    this.loadingPromises = new Map()
    this.fallbackEnabled = ANALYSIS_CONFIG.AI_FALLBACK_ENABLED
  }

  /**
   * 注册AI模型
   * @param {string} name - 模型名称
   * @param {Function} loader - 模型加载函数
   * @param {Object} config - 模型配置
   */
  registerModel(name, loader, config = {}) {
    this.models.set(name, {
      loader,
      config,
      instance: null,
      loaded: false,
      error: null
    })
  }

  /**
   * 异步加载模型
   * @param {string} name - 模型名称
   * @returns {Promise} 加载Promise
   */
  async loadModel(name) {
    if (this.loadingPromises.has(name)) {
      return this.loadingPromises.get(name)
    }

    const model = this.models.get(name)
    if (!model) {
      throw new Error(`Model ${name} not registered`)
    }

    if (model.loaded && model.instance) {
      return model.instance
    }

    const loadingPromise = this._loadModelWithTimeout(model, name)
    this.loadingPromises.set(name, loadingPromise)

    try {
      const instance = await loadingPromise
      model.instance = instance
      model.loaded = true
      model.error = null
      return instance
    } catch (error) {
      model.error = error
      model.loaded = false
      console.warn(`Failed to load AI model ${name}:`, error)
      throw error
    } finally {
      this.loadingPromises.delete(name)
    }
  }

  /**
   * 带超时的模型加载
   * @param {Object} model - 模型对象
   * @param {string} name - 模型名称
   * @returns {Promise} 加载Promise
   */
  async _loadModelWithTimeout(model, name) {
    const timeout = model.config.timeout || ANALYSIS_CONFIG.AI_MODEL_TIMEOUT

    return Promise.race([
      model.loader(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Model ${name} load timeout`)), timeout)
      )
    ])
  }

  /**
   * 获取可用的模型实例
   * @param {string} name - 模型名称
   * @returns {Object|null} 模型实例
   */
  getModel(name) {
    const model = this.models.get(name)
    return model?.loaded ? model.instance : null
  }

  /**
   * 检查模型是否可用
   * @param {string} name - 模型名称
   * @returns {boolean} 是否可用
   */
  isModelAvailable(name) {
    const model = this.models.get(name)
    return model?.loaded && model.instance && !model.error
  }
}

// 全局AI模型管理器实例
const aiModelManager = new AIModelManager()

/**
 * 注册TensorFlow.js模型（示例）
 */
function registerTensorFlowModel() {
  aiModelManager.registerModel('tensorflow', async () => {
    try {
      // 动态导入TensorFlow.js
      await import('@tensorflow/tfjs')

      // 这里可以加载预训练的文本分类模型
      // const model = await tf.loadLayersModel('/models/logistics-classifier/model.json')

      return {
        name: 'tensorflow',
        predict: async (text) => {
          // 模拟预测逻辑
          return await simulateTextClassification(text)
        }
      }
    } catch (error) {
      console.warn('TensorFlow.js not available:', error)
      throw error
    }
  }, { timeout: 5000 })
}

/**
 * 注册Transformers.js模型（示例）
 */
function registerTransformersModel() {
  aiModelManager.registerModel('transformers', async () => {
    try {
      // 动态导入Transformers.js
      const { pipeline } = await import('@xenova/transformers')

      // 创建文本分类管道
      const classifier = await pipeline('text-classification', 'Xenova/distilbert-base-uncased-finetuned-sst-2-english')

      return {
        name: 'transformers',
        predict: async (text) => {
          const result = await classifier(text)
          return result
        }
      }
    } catch (error) {
      console.warn('Transformers.js not available:', error)
      throw error
    }
  }, { timeout: 8000 })
}

/**
 * 模拟文本分类（兜底方案）
 * @param {string} text - 输入文本
 * @returns {Promise<Object>} 分类结果
 */
async function simulateTextClassification(text) {
  // 基于规则的简单分类逻辑
  const lowerText = text.toLowerCase()

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 100))

  let confidence = 0.5
  let predictedStatus = LOGISTICS_STATUS.IN_TRANSIT

  // 简单的关键词匹配
  if (lowerText.includes('签收') || lowerText.includes('妥投')) {
    predictedStatus = LOGISTICS_STATUS.DELIVERED
    confidence = 0.9
  } else if (lowerText.includes('派送') || lowerText.includes('配送')) {
    predictedStatus = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.8
  } else if (lowerText.includes('异常') || lowerText.includes('失败')) {
    predictedStatus = LOGISTICS_STATUS.EXCEPTION
    confidence = 0.85
  } else if (lowerText.includes('揽收') || lowerText.includes('收件')) {
    predictedStatus = LOGISTICS_STATUS.PICKED_UP
    confidence = 0.8
  }

  return {
    status: predictedStatus,
    confidence,
    source: 'simulation'
  }
}

/**
 * AI增强语义分析
 * @param {string} context - 轨迹描述文本
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} AI分析结果
 */
export async function aiEnhancedAnalysis(context, options = {}) {
  const result = {
    aiPrediction: null,
    confidence: 0,
    source: 'fallback',
    error: null,
    fallbackUsed: false
  }

  try {
    // 尝试使用AI模型进行分析
    const aiResult = await tryAIModels(context, options)

    if (aiResult) {
      result.aiPrediction = aiResult.status
      result.confidence = aiResult.confidence
      result.source = aiResult.source
      return result
    }
  } catch (error) {
    result.error = error
    console.warn('AI analysis failed, using fallback:', error)
  }

  // 使用兜底分析
  if (aiModelManager.fallbackEnabled) {
    try {
      const fallbackResult = await fallbackAnalysis(context)
      result.aiPrediction = fallbackResult.status
      result.confidence = fallbackResult.confidence
      result.source = 'fallback'
      result.fallbackUsed = true
    } catch (fallbackError) {
      result.error = fallbackError
      console.error('Fallback analysis also failed:', fallbackError)
    }
  }

  return result
}

/**
 * 尝试使用多个AI模型
 * @param {string} context - 文本内容
 * @param {Object} options - 选项
 * @returns {Promise<Object|null>} AI分析结果
 */
async function tryAIModels(context, options) {
  const modelPriority = options.modelPriority || ['transformers', 'tensorflow']

  for (const modelName of modelPriority) {
    try {
      if (aiModelManager.isModelAvailable(modelName)) {
        const model = aiModelManager.getModel(modelName)
        const result = await model.predict(context)

        if (result && result.confidence > 0.3) {
          return {
            status: result.status || result.label,
            confidence: result.confidence || result.score,
            source: modelName
          }
        }
      } else {
        // 尝试加载模型
        try {
          const model = await aiModelManager.loadModel(modelName)
          const result = await model.predict(context)

          if (result && result.confidence > 0.3) {
            return {
              status: result.status || result.label,
              confidence: result.confidence || result.score,
              source: modelName
            }
          }
        } catch (loadError) {
          console.warn(`Failed to load model ${modelName}:`, loadError)
          continue
        }
      }
    } catch (error) {
      console.warn(`Model ${modelName} prediction failed:`, error)
      continue
    }
  }

  return null
}

/**
 * 兜底分析方案
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} 分析结果
 */
async function fallbackAnalysis(context) {
  // 使用增强的规则引擎作为兜底
  const sentimentScore = calculateSentiment(context)
  const keywordAnalysis = analyzeKeywordPatterns(context)

  let status = LOGISTICS_STATUS.IN_TRANSIT
  let confidence = 0.4

  // 基于情感和关键词的综合判断
  if (sentimentScore > 0.5 && keywordAnalysis.completionWords > 0) {
    status = LOGISTICS_STATUS.DELIVERED
    confidence = 0.7
  } else if (sentimentScore < -0.5 || keywordAnalysis.problemWords > 0) {
    status = LOGISTICS_STATUS.EXCEPTION
    confidence = 0.6
  } else if (keywordAnalysis.deliveryWords > 0) {
    status = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.6
  } else if (keywordAnalysis.pickupWords > 0) {
    status = LOGISTICS_STATUS.PICKED_UP
    confidence = 0.6
  }

  return { status, confidence, source: 'fallback' }
}

/**
 * 计算文本情感分数
 * @param {string} text - 文本内容
 * @returns {number} 情感分数 (-1到1)
 */
function calculateSentiment(text) {
  const { positive, negative } = SENTIMENT_WORDS
  let positiveCount = 0
  let negativeCount = 0

  const lowerText = text.toLowerCase()

  positive.forEach(word => {
    if (lowerText.includes(word)) positiveCount++
  })

  negative.forEach(word => {
    if (lowerText.includes(word)) negativeCount++
  })

  const total = positiveCount + negativeCount
  if (total === 0) return 0

  return (positiveCount - negativeCount) / total
}

/**
 * 分析关键词模式
 * @param {string} text - 文本内容
 * @returns {Object} 关键词分析结果
 */
function analyzeKeywordPatterns(text) {
  const lowerText = text.toLowerCase()

  const patterns = {
    completionWords: ['完成', '成功', '签收', '妥投'],
    problemWords: ['失败', '异常', '错误', '问题'],
    deliveryWords: ['派送', '配送', '投递'],
    pickupWords: ['揽收', '收件', '取件'],
    transitWords: ['运输', '转运', '途中']
  }

  const result = {}

  Object.keys(patterns).forEach(key => {
    result[key] = patterns[key].filter(word => lowerText.includes(word)).length
  })

  return result
}

// 初始化AI模型（可选）
export function initializeAIModels() {
  try {
    registerTensorFlowModel()
    registerTransformersModel()
  } catch (error) {
    console.warn('Failed to register AI models:', error)
  }
}

// 导出AI模型管理器（供外部使用）
export { aiModelManager }
