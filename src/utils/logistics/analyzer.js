/**
 * 物流状态分析器 - 核心分析引擎
 * 整合关键词匹配、AI分析、语义分析等多种技术
 */

import nlp from 'compromise'
import {
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  ANALYSIS_CONFIG,
  isFinalStatus,
  getPossibleNextStates,
  isValidTransition
} from './constants.js'
import { STATUS_KEYWORDS } from './keywords.js'
import { aiEnhancedAnalysis } from './aiAnalyzer.js'
import { withCache, withDebounce, analysisResultPool } from './performance.js'

/**
 * 分析物流状态 - 主入口函数
 * @param {Array} orderTrack - 物流轨迹数组，按时间倒序排列
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeLogisticsStatus(orderTrack, options = {}) {
  try {
    if (!orderTrack || orderTrack.length === 0) {
      return createDefaultResult()
    }

    // 获取最新的轨迹记录
    const latestTrack = orderTrack[0]
    const context = latestTrack.context || latestTrack.content || ''

    // 优化策略：优先分析最新记录
    const latestAnalysis = await analyzeTrackContext(context, latestTrack, options)

    // 如果最新记录分析置信度足够高且不是模糊状态，直接返回结果
    if (latestAnalysis.confidence >= ANALYSIS_CONFIG.HIGH_CONFIDENCE_THRESHOLD &&
        !isAmbiguousContext(context)) {
      return {
        ...latestAnalysis,
        latestTrack,
        analysisMethod: 'latest_record_high_confidence'
      }
    }

    // 如果最新记录置信度不够或者是模糊状态，进行全轨迹分析
    const fullTrackAnalysis = await analyzeFullTrackHistory(orderTrack, options)

    // 比较两种分析结果，选择更可靠的
    const finalResult = selectBestAnalysis(latestAnalysis, fullTrackAnalysis)

    return {
      ...finalResult,
      latestTrack,
      latestAnalysis,
      fullTrackAnalysis
    }
  } catch (error) {
    console.error('Logistics analysis failed:', error)
    return createErrorResult(error)
  }
}

/**
 * 分析单条轨迹上下文
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 完整的轨迹记录
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
async function analyzeTrackContext(context, trackRecord = {}, options = {}) {
  const result = analysisResultPool.acquire()

  try {
    // 1. 关键词匹配分析
    const keywordAnalysis = analyzeKeywords(context)

    // 2. 语义增强分析
    const semanticAnalysis = await enhancedSemanticAnalysis(context, options)

    // 3. 时间因素分析
    const timeAnalysis = analyzeTimeFactors(trackRecord)

    // 4. AI增强分析（如果启用）
    let aiAnalysis = null
    if (options.enableAI !== false) {
      try {
        aiAnalysis = await aiEnhancedAnalysis(context, options)
      } catch (error) {
        console.warn('AI analysis failed:', error)
      }
    }

    // 综合计算最终结果
    const finalResult = combineAnalysisResults({
      keyword: keywordAnalysis,
      semantic: semanticAnalysis,
      time: timeAnalysis,
      ai: aiAnalysis
    }, context)

    // 填充结果对象
    Object.assign(result, {
      status: finalResult.status,
      statusText: STATUS_TEXT_MAP[finalResult.status],
      confidence: finalResult.confidence,
      rawScore: finalResult.rawScore,
      keywordScores: keywordAnalysis.statusScores,
      semanticAnalysis,
      timeAnalysis,
      aiAnalysis,
      analysisMethod: 'single_track'
    })

    return result
  } catch (error) {
    analysisResultPool.release(result)
    throw error
  }
}

/**
 * 关键词分析
 * @param {string} context - 文本内容
 * @returns {Object} 关键词分析结果
 */
function analyzeKeywords(context) {
  let maxScore = 0
  let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP
  const statusScores = {}

  // 遍历所有状态的关键词
  for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
    const score = calculateKeywordScore(context, keywords)
    statusScores[status] = score

    if (score > maxScore) {
      maxScore = score
      detectedStatus = status
    }
  }

  return {
    status: detectedStatus,
    score: maxScore,
    confidence: Math.min(maxScore / 50, 1),
    statusScores
  }
}

/**
 * 计算关键词匹配分数
 * @param {string} text - 待分析文本
 * @param {Object} keywords - 分级关键词对象
 * @returns {number} 匹配分数
 */
function calculateKeywordScore(text, keywords) {
  let score = 0
  const lowerText = text.toLowerCase()
  const textLength = text.length

  // 动态权重配置
  const lengthFactor = Math.min(textLength / 20, 2)
  const weights = {
    high: Math.round(ANALYSIS_CONFIG.KEYWORD_WEIGHTS.high / lengthFactor),
    medium: Math.round(ANALYSIS_CONFIG.KEYWORD_WEIGHTS.medium / lengthFactor),
    low: Math.round(ANALYSIS_CONFIG.KEYWORD_WEIGHTS.low / lengthFactor)
  }

  for (const [level, keywordList] of Object.entries(keywords)) {
    const weight = weights[level] || 8

    for (const keyword of keywordList) {
      const lowerKeyword = keyword.toLowerCase()
      let matchScore = 0

      // 支持正则表达式匹配
      if (keyword.includes('.*')) {
        try {
          const regex = new RegExp(lowerKeyword, 'i')
          if (regex.test(lowerText)) {
            matchScore = weight * 1.5
          }
        } catch {
          const fallbackKeyword = lowerKeyword.replace(/\.\*/g, '')
          if (lowerText.includes(fallbackKeyword)) {
            matchScore = weight
          }
        }
      } else {
        if (lowerText.includes(lowerKeyword)) {
          const matchQuality = calculateMatchQuality(lowerText, lowerKeyword)
          matchScore = weight * matchQuality
        }
      }

      if (matchScore > 0) {
        const occurrences = (lowerText.match(
          new RegExp(lowerKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        ) || []).length
        const occurrenceBonus = Math.min(occurrences * 0.2, 1)
        score += matchScore * (1 + occurrenceBonus)
      }
    }
  }

  return Math.round(score)
}

/**
 * 计算关键词匹配质量
 * @param {string} text - 文本
 * @param {string} keyword - 关键词
 * @returns {number} 匹配质量系数
 */
function calculateMatchQuality(text, keyword) {
  if (text.trim() === keyword) return 2.0

  const wordBoundaryRegex = new RegExp(
    `\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i'
  )
  if (wordBoundaryRegex.test(text)) return 1.5
  if (text.startsWith(keyword)) return 1.3
  if (text.endsWith(keyword)) return 1.2

  return 1.0
}

/**
 * 增强语义分析
 * @param {string} context - 文本内容
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 语义分析结果
 */
async function enhancedSemanticAnalysis(context) {
  try {
    // 使用 compromise 进行语义分析
    const compromiseResult = analyzeWithCompromise(context)

    // 基础模式匹配
    const patternAnalysis = analyzePatterns(context)

    // 上下文连贯性分析
    const coherenceScore = analyzeContextCoherence(context)

    const semanticScore = patternAnalysis.score + compromiseResult.semanticBonus + coherenceScore

    return {
      semanticScore: Math.max(-ANALYSIS_CONFIG.SEMANTIC_BONUS_LIMIT,
                             Math.min(ANALYSIS_CONFIG.SEMANTIC_BONUS_LIMIT, semanticScore)),
      coherence: coherenceScore,
      intensity: Math.abs(semanticScore) > 8 ? 'high' : 'normal',
      compromiseAnalysis: compromiseResult,
      patternAnalysis
    }
  } catch {
    console.warn('Semantic analysis failed')
    return {
      semanticScore: 0,
      coherence: 0,
      intensity: 'normal',
      compromiseAnalysis: null,
      patternAnalysis: null
    }
  }
}

/**
 * 使用 compromise 库进行语义分析
 * @param {string} context - 轨迹描述
 * @returns {Object} compromise 分析结果
 */
function analyzeWithCompromise(context) {
  try {
    const doc = nlp(context)

    // 提取语言要素
    const verbs = doc.verbs().out('array')
    const nouns = doc.nouns().out('array')
    const places = doc.places().out('array')
    const people = doc.people().out('array')

    // 情感分析
    const sentiment = analyzeSentimentWithCompromise(doc)

    // 时态分析
    const tenseAnalysis = analyzeTensePattern(doc)

    // 计算语义加成分数
    let semanticBonus = 0

    // 动词完成度分析
    if (verbs.some(verb => ['delivered', 'completed', 'finished', 'signed'].includes(verb.toLowerCase()))) {
      semanticBonus += 8
    }

    // 时态加成
    if (tenseAnalysis.isProgressive) semanticBonus += 5
    if (tenseAnalysis.isPast) semanticBonus += 6

    // 地点和人员信息加成
    if (places.length > 0) semanticBonus += 3
    if (people.length > 0) semanticBonus += 2

    // 情感极性加成
    semanticBonus += sentiment.polarity * 3

    return {
      verbs,
      nouns,
      places,
      people,
      sentiment,
      tenseAnalysis,
      semanticBonus: Math.max(-10, Math.min(10, semanticBonus))
    }
  } catch (error) {
    console.warn('Compromise analysis failed:', error)
    return {
      verbs: [],
      nouns: [],
      places: [],
      people: [],
      sentiment: { polarity: 0, confidence: 0 },
      tenseAnalysis: { isPast: false, isProgressive: false },
      semanticBonus: 0
    }
  }
}

/**
 * 使用 compromise 进行情感分析
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 情感分析结果
 */
function analyzeSentimentWithCompromise(doc) {
  const adjectives = doc.adjectives().out('array')

  const positiveWords = ['successful', 'completed', 'delivered', 'good', 'normal', '成功', '完成', '正常', '顺利']
  const negativeWords = ['failed', 'error', 'problem', 'delayed', 'unable', '失败', '错误', '延误', '异常']

  let positiveScore = 0
  let negativeScore = 0

  adjectives.forEach(adj => {
    const lowerAdj = adj.toLowerCase()
    if (positiveWords.some(word => lowerAdj.includes(word))) positiveScore++
    if (negativeWords.some(word => lowerAdj.includes(word))) negativeScore++
  })

  const text = doc.out('text').toLowerCase()
  positiveWords.forEach(word => {
    if (text.includes(word)) positiveScore++
  })
  negativeWords.forEach(word => {
    if (text.includes(word)) negativeScore++
  })

  const polarity = positiveScore - negativeScore
  const confidence = Math.abs(polarity) / Math.max(positiveScore + negativeScore, 1)

  return { positive: positiveScore, negative: negativeScore, polarity, confidence }
}

/**
 * 分析时态模式
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 时态分析结果
 */
function analyzeTensePattern(doc) {
  const verbs = doc.verbs()

  const pastVerbs = verbs.toPastTense().out('array')
  const isPast = pastVerbs.length > 0

  const progressiveVerbs = verbs.toGerund().out('array')
  const isProgressive = progressiveVerbs.length > 0 || doc.has('#Gerund')

  const perfectVerbs = verbs.conjugate().map(v => v.PastTense).filter(Boolean)
  const isPerfect = perfectVerbs.length > 0

  return {
    isPast,
    isProgressive,
    isPerfect,
    dominantTense: isPast ? 'past' : isProgressive ? 'progressive' : 'present'
  }
}

/**
 * 分析文本模式
 * @param {string} context - 文本内容
 * @returns {Object} 模式分析结果
 */
function analyzePatterns(context) {
  const negativePatterns = ['未.*成功', '尚未.*', '暂未.*', '等待.*', '准备.*', '即将.*']
  const positivePatterns = ['已.*完成', '成功.*', '顺利.*', '正常.*']
  const strongPositive = ['妥投', '签收成功', '投递完成']
  const strongNegative = ['派送失败', '拒收', '异常', '丢失']

  let score = 0

  strongPositive.forEach(pattern => {
    if (context.includes(pattern)) score += 10
  })

  strongNegative.forEach(pattern => {
    if (context.includes(pattern)) score -= 10
  })

  negativePatterns.forEach(pattern => {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) score -= 5
  })

  positivePatterns.forEach(pattern => {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) score += 5
  })

  return { score, patterns: { strongPositive, strongNegative, positive: positivePatterns, negative: negativePatterns } }
}

/**
 * 分析上下文连贯性
 * @param {string} context - 轨迹描述
 * @returns {number} 连贯性分数
 */
function analyzeContextCoherence(context) {
  let coherenceScore = 0

  const timeLogicWords = ['然后', '接着', '随后', '之后', '现在', '目前']
  const causalWords = ['因为', '由于', '所以', '因此', '导致']
  const locationWords = ['从', '到', '经过', '途径', '抵达']

  timeLogicWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  causalWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 3
  })

  locationWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  return Math.min(coherenceScore, ANALYSIS_CONFIG.COHERENCE_BONUS_LIMIT)
}

/**
 * 分析时间因素
 * @param {Object} trackRecord - 轨迹记录
 * @returns {Object} 时间分析结果
 */
function analyzeTimeFactors(trackRecord) {
  let timeBonus = 0
  const timestamp = trackRecord.msgTime || trackRecord.time

  if (!timestamp) {
    return { timeBonus: 0, hasValidTime: false }
  }

  try {
    const trackTime = new Date(timestamp)
    const now = new Date()
    const timeDiff = now - trackTime
    const hoursDiff = timeDiff / (1000 * 60 * 60)

    if (hoursDiff <= ANALYSIS_CONFIG.TIME_BONUS.RECENT_HOURS) {
      timeBonus += 5
    } else if (hoursDiff <= ANALYSIS_CONFIG.TIME_BONUS.DAILY_HOURS) {
      timeBonus += 3
    } else if (hoursDiff <= ANALYSIS_CONFIG.TIME_BONUS.WEEKLY_HOURS) {
      timeBonus += 1
    }

    return {
      timeBonus,
      hasValidTime: true,
      hoursSinceUpdate: hoursDiff,
      isRecent: hoursDiff <= ANALYSIS_CONFIG.TIME_BONUS.DAILY_HOURS
    }
  } catch (error) {
    return { timeBonus: 0, hasValidTime: false }
  }
}

/**
 * 检测是否为模糊上下文
 * @param {string} context - 轨迹描述
 * @returns {boolean} 是否为模糊上下文
 */
function isAmbiguousContext(context) {
  const ambiguousKeywords = [
    '处理中', '处理', '操作中', '进行中', '执行中',
    '快件', '包裹', '订单', '货物', '商品',
    '正在', '进行', '执行', '操作'
  ]

  const lowerContext = context.toLowerCase()

  if (context.length < 10) {
    return ambiguousKeywords.some(keyword => lowerContext.includes(keyword))
  }

  const ambiguousCount = ambiguousKeywords.filter(keyword =>
    lowerContext.includes(keyword)
  ).length

  return ambiguousCount >= 2 && context.length < 20
}

/**
 * 综合分析结果
 * @param {Object} analyses - 各种分析结果
 * @param {string} context - 原始文本
 * @returns {Object} 综合结果
 */
function combineAnalysisResults(analyses, context) {
  const { keyword, semantic, time, ai } = analyses

  let finalStatus = keyword.status
  let finalScore = keyword.score
  let finalConfidence = keyword.confidence

  // 加入语义分析加成
  finalScore += semantic.semanticScore

  // 加入时间因素加成
  finalScore += time.timeBonus

  // AI分析结果处理
  if (ai && ai.aiPrediction && ai.confidence > 0.5) {
    if (keyword.score >= 25) {
      // 关键词匹配得分很高时，优先相信关键词匹配
      finalScore += 5
    } else {
      // 关键词匹配得分不高时，考虑AI建议
      if (ai.aiPrediction !== finalStatus) {
        const currentConfidence = Math.min(finalScore / 50, 1)
        if (ai.confidence > currentConfidence) {
          finalStatus = ai.aiPrediction
          finalScore = ai.confidence * 50
        }
      } else {
        // 状态一致时，提升置信度
        finalScore += 8
      }
    }
  }

  // 计算最终置信度
  finalConfidence = Math.min(finalScore / 50, 1)
  finalConfidence = Math.max(finalConfidence, 0.1)

  return {
    status: finalStatus,
    confidence: finalConfidence,
    rawScore: finalScore
  }
}

/**
 * 分析完整物流轨迹历史
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
async function analyzeFullTrackHistory(orderTrack, options = {}) {
  if (!orderTrack || orderTrack.length === 0) {
    return createDefaultResult('full_track_empty')
  }

  const statusSequence = []
  const trackAnalyses = []

  // 分析每条轨迹记录（最多分析5条）
  const maxTracks = Math.min(orderTrack.length, ANALYSIS_CONFIG.MAX_TRACK_ANALYSIS)

  for (let i = 0; i < maxTracks; i++) {
    const track = orderTrack[i]
    const context = track.context || track.content || ''

    try {
      const analysis = await analyzeTrackContext(context, track, options)

      trackAnalyses.push({
        ...analysis,
        trackIndex: i,
        timestamp: track.msgTime || track.time,
        isLatest: i === 0
      })

      statusSequence.push(analysis.status)
    } catch (error) {
      console.warn(`Failed to analyze track ${i}:`, error)
    }
  }

  // 状态一致性检查
  const consistencyAnalysis = analyzeStatusConsistency(statusSequence)

  // 状态转换逻辑验证
  const transitionAnalysis = validateStatusTransitions(statusSequence)

  // 综合分析得出最终状态
  const finalStatus = determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis)

  // 如果最新记录很模糊，尝试从历史轨迹推断当前状态
  if (trackAnalyses.length > 1 && trackAnalyses[0].confidence < ANALYSIS_CONFIG.MEDIUM_CONFIDENCE_THRESHOLD) {
    const inferredStatus = inferStatusFromHistory(trackAnalyses)
    if (inferredStatus && inferredStatus.confidence > finalStatus.confidence) {
      return {
        ...inferredStatus,
        statusSequence,
        trackAnalyses,
        consistencyAnalysis,
        transitionAnalysis,
        analysisMethod: 'inferred_from_history'
      }
    }
  }

  return {
    ...finalStatus,
    statusSequence,
    trackAnalyses,
    consistencyAnalysis,
    transitionAnalysis,
    analysisMethod: 'full_track_analysis'
  }
}

/**
 * 选择最佳分析结果
 * @param {Object} latestAnalysis - 最新记录分析结果
 * @param {Object} fullTrackAnalysis - 全轨迹分析结果
 * @returns {Object} 最终分析结果
 */
function selectBestAnalysis(latestAnalysis, fullTrackAnalysis) {
  // 如果最新记录置信度很高且是终态，优先选择
  if (latestAnalysis.confidence >= ANALYSIS_CONFIG.HIGH_CONFIDENCE_THRESHOLD &&
      isFinalStatus(latestAnalysis.status)) {
    return {
      ...latestAnalysis,
      analysisMethod: 'latest_high_confidence_final'
    }
  }

  // 如果全轨迹分析置信度更高，选择全轨迹结果
  if (fullTrackAnalysis.confidence > latestAnalysis.confidence + 0.2) {
    return {
      ...fullTrackAnalysis,
      analysisMethod: 'full_track_higher_confidence'
    }
  }

  // 如果状态一致且都有合理置信度，选择置信度更高的
  if (latestAnalysis.status === fullTrackAnalysis.status) {
    const bestAnalysis = latestAnalysis.confidence >= fullTrackAnalysis.confidence
      ? latestAnalysis
      : fullTrackAnalysis

    return {
      ...bestAnalysis,
      confidence: Math.max(latestAnalysis.confidence, fullTrackAnalysis.confidence),
      analysisMethod: 'consistent_status_best_confidence'
    }
  }

  // 默认选择最新记录分析，但降低置信度
  return {
    ...latestAnalysis,
    confidence: Math.max(latestAnalysis.confidence * 0.8, 0.1),
    analysisMethod: 'latest_with_reduced_confidence'
  }
}

/**
 * 分析状态一致性
 * @param {Array} statusSequence - 状态序列
 * @returns {Object} 一致性分析结果
 */
function analyzeStatusConsistency(statusSequence) {
  if (statusSequence.length <= 1) {
    return {
      isConsistent: true,
      confidence: 1,
      dominantStatus: statusSequence[0] || LOGISTICS_STATUS.PENDING_PICKUP
    }
  }

  // 统计各状态出现频率
  const statusCount = {}
  statusSequence.forEach(status => {
    statusCount[status] = (statusCount[status] || 0) + 1
  })

  const totalCount = statusSequence.length
  const maxCount = Math.max(...Object.values(statusCount))
  const dominantStatus = Object.keys(statusCount).find(status => statusCount[status] === maxCount)

  const consistency = maxCount / totalCount

  return {
    isConsistent: consistency >= 0.6,
    confidence: consistency,
    dominantStatus,
    statusDistribution: statusCount
  }
}

/**
 * 验证状态转换逻辑
 * @param {Array} statusSequence - 状态序列（按时间倒序）
 * @returns {Object} 转换验证结果
 */
function validateStatusTransitions(statusSequence) {
  if (statusSequence.length <= 1) {
    return { isValid: true, confidence: 1, invalidTransitions: [] }
  }

  const invalidTransitions = []
  let validTransitionCount = 0

  // 检查相邻状态转换的合理性（注意：statusSequence是倒序的）
  for (let i = 0; i < statusSequence.length - 1; i++) {
    const currentStatus = statusSequence[i] // 较新的状态
    const previousStatus = statusSequence[i + 1] // 较旧的状态

    if (isValidTransition(previousStatus, currentStatus) || currentStatus === previousStatus) {
      validTransitionCount++
    } else {
      invalidTransitions.push({
        from: previousStatus,
        to: currentStatus,
        index: i
      })
    }
  }

  const totalTransitions = statusSequence.length - 1
  const validityRatio = totalTransitions > 0 ? validTransitionCount / totalTransitions : 1

  return {
    isValid: validityRatio >= 0.7,
    confidence: validityRatio,
    invalidTransitions,
    validTransitionCount,
    totalTransitions
  }
}

/**
 * 确定最终状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @param {Object} consistencyAnalysis - 一致性分析结果
 * @param {Object} transitionAnalysis - 转换分析结果
 * @returns {Object} 最终状态结果
 */
function determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis) {
  if (trackAnalyses.length === 0) {
    return createDefaultResult()
  }

  // 获取最新记录的分析结果
  const latestAnalysis = trackAnalyses[0]

  // 如果最新记录是终态且置信度高，直接返回
  if (isFinalStatus(latestAnalysis.status) && latestAnalysis.confidence >= 0.7) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: Math.min(latestAnalysis.confidence + 0.1, 1)
    }
  }

  // 如果状态转换逻辑有效且一致性好，使用主导状态
  if (transitionAnalysis.isValid && consistencyAnalysis.isConsistent) {
    const finalStatus = consistencyAnalysis.dominantStatus
    const baseConfidence = Math.max(
      latestAnalysis.confidence,
      consistencyAnalysis.confidence * 0.8
    )

    return {
      status: finalStatus,
      statusText: STATUS_TEXT_MAP[finalStatus],
      confidence: Math.min(baseConfidence + 0.15, 1)
    }
  }

  // 如果转换逻辑无效，但最新记录置信度可接受，使用最新记录
  if (latestAnalysis.confidence >= ANALYSIS_CONFIG.MEDIUM_CONFIDENCE_THRESHOLD) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: latestAnalysis.confidence * 0.9
    }
  }

  // 最后的兜底策略：使用主导状态但降低置信度
  const fallbackStatus = consistencyAnalysis.dominantStatus || LOGISTICS_STATUS.PENDING_PICKUP
  return {
    status: fallbackStatus,
    statusText: STATUS_TEXT_MAP[fallbackStatus],
    confidence: Math.max(consistencyAnalysis.confidence * 0.6, 0.1)
  }
}

/**
 * 从历史轨迹推断当前状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @returns {Object|null} 推断结果
 */
function inferStatusFromHistory(trackAnalyses) {
  if (trackAnalyses.length < 2) return null

  // 获取最近几条有意义的轨迹
  const meaningfulTracks = trackAnalyses.filter(track =>
    track.confidence > ANALYSIS_CONFIG.LOW_CONFIDENCE_THRESHOLD
  )

  if (meaningfulTracks.length === 0) return null

  // 找到最近的高置信度状态
  const lastHighConfidenceTrack = meaningfulTracks.find(track =>
    track.confidence >= 0.7
  )

  if (!lastHighConfidenceTrack) return null

  // 基于最后的高置信度状态推断当前可能的状态
  const lastStatus = lastHighConfidenceTrack.status
  const possibleNextStates = getPossibleNextStates(lastStatus)

  // 如果只有一个可能的下一状态，使用它
  if (possibleNextStates.length === 1) {
    return {
      status: possibleNextStates[0],
      statusText: STATUS_TEXT_MAP[possibleNextStates[0]],
      confidence: Math.min(lastHighConfidenceTrack.confidence * 0.8, 0.7)
    }
  }

  // 如果有多个可能状态，选择最合理的
  const mostLikelyStatus = selectMostLikelyNextStatus(lastStatus, trackAnalyses)

  return mostLikelyStatus ? {
    status: mostLikelyStatus,
    statusText: STATUS_TEXT_MAP[mostLikelyStatus],
    confidence: Math.min(lastHighConfidenceTrack.confidence * 0.7, 0.6)
  } : null
}

/**
 * 选择最可能的下一状态
 * @param {string} lastStatus - 最后的明确状态
 * @param {Array} trackAnalyses - 轨迹分析结果
 * @returns {string|null} 最可能的状态
 */
function selectMostLikelyNextStatus(lastStatus) {
  const possibleStates = getPossibleNextStates(lastStatus)

  if (possibleStates.length === 0) return lastStatus
  if (possibleStates.length === 1) return possibleStates[0]

  // 基于时间和常见流程选择
  if (lastStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
    return LOGISTICS_STATUS.DELIVERED
  }

  if (lastStatus === LOGISTICS_STATUS.FAILED_DELIVERY) {
    return LOGISTICS_STATUS.OUT_FOR_DELIVERY
  }

  return possibleStates[0]
}

/**
 * 创建默认结果
 * @param {string} method - 分析方法
 * @returns {Object} 默认结果
 */
function createDefaultResult(method = 'default') {
  return {
    status: LOGISTICS_STATUS.PENDING_PICKUP,
    statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
    latestTrack: null,
    confidence: 0,
    analysisMethod: method
  }
}

/**
 * 创建错误结果
 * @param {Error} error - 错误对象
 * @returns {Object} 错误结果
 */
function createErrorResult(error) {
  return {
    status: LOGISTICS_STATUS.EXCEPTION,
    statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.EXCEPTION],
    latestTrack: null,
    confidence: 0.1,
    error: error.message,
    analysisMethod: 'error_fallback'
  }
}

// 导出带缓存和防抖的分析函数
export const analyzeLogisticsStatusCached = withCache(analyzeLogisticsStatus)
export const analyzeLogisticsStatusDebounced = withDebounce(analyzeLogisticsStatus, 300)
