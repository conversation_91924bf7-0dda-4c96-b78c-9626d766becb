/**
 * 物流状态语义分析器 - 兼容性包装器
 *
 * 注意：此文件已重构为新的模块化架构的兼容性包装器
 * 新的实现位于 ./logistics/ 目录下，提供了以下增强功能：
 *
 * 1. 大幅扩充的中文关键词库
 * 2. AI增强分析（支持TensorFlow.js、Transformers.js等）
 * 3. 高性能缓存和批处理
 * 4. 完善的错误处理和兜底机制
 * 5. 模块化架构，易于维护和扩展
 *
 * 推荐使用新的API：
 * import { createAnalyzer, quickAnalyze } from './logistics/index.js'
 */

// 导入新的模块化实现
import {
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  isFinalStatus,
  getStatusUrgency,
  createAnalyzer,
  quickAnalyze,
  getStatusInfo
} from './logistics/index.js'

// 为了向后兼容，保持原有的导出
export {
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  isFinalStatus,
  getStatusUrgency
}

// 创建默认分析器实例（用于向后兼容）
let defaultAnalyzer = null

/**
 * 获取默认分析器实例
 * @returns {LogisticsStatusAnalyzer} 分析器实例
 */
function getDefaultAnalyzer() {
  if (!defaultAnalyzer) {
    defaultAnalyzer = createAnalyzer({
      enableAI: true,
      enableCache: true,
      enableErrorHandling: true
    })
  }
  return defaultAnalyzer
}

/**
 * 分析物流状态 - 兼容性函数
 * @param {Array} orderTrack - 物流轨迹数组，按时间倒序排列
 * @param {Object} options - 分析选项（可选）
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeLogisticsStatus(orderTrack, options = {}) {
  try {
    const analyzer = getDefaultAnalyzer()
    return await analyzer.analyze(orderTrack, options)
  } catch (error) {
    console.error('Analysis failed, using fallback:', error)

    // 兜底处理：返回基本结果
    if (!orderTrack || orderTrack.length === 0) {
      return {
        status: LOGISTICS_STATUS.PENDING_PICKUP,
        statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
        latestTrack: null,
        confidence: 0,
        analysisMethod: 'fallback_default',
        error: error.message
      }
    }

    // 简单的关键词匹配兜底
    const latestTrack = orderTrack[0]
    const context = (latestTrack.context || latestTrack.content || '').toLowerCase()

    let status = LOGISTICS_STATUS.IN_TRANSIT
    let confidence = 0.3

    if (context.includes('签收') || context.includes('妥投')) {
      status = LOGISTICS_STATUS.DELIVERED
      confidence = 0.8
    } else if (context.includes('派送') || context.includes('配送')) {
      status = LOGISTICS_STATUS.OUT_FOR_DELIVERY
      confidence = 0.7
    } else if (context.includes('异常') || context.includes('失败')) {
      status = LOGISTICS_STATUS.EXCEPTION
      confidence = 0.7
    } else if (context.includes('揽收') || context.includes('收件')) {
      status = LOGISTICS_STATUS.PICKED_UP
      confidence = 0.7
    }

    return {
      status,
      statusText: STATUS_TEXT_MAP[status],
      latestTrack,
      confidence,
      analysisMethod: 'fallback_simple',
      error: error.message
    }
  }
}

/**
 * 获取详细的物流状态分析报告 - 兼容性函数
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项（可选）
 * @returns {Promise<Object>} 详细分析报告
 */
export async function getDetailedLogisticsAnalysis(orderTrack, options = {}) {
  try {
    const analyzer = getDefaultAnalyzer()
    return await analyzer.getDetailedAnalysis(orderTrack, options)
  } catch (error) {
    console.error('Detailed analysis failed:', error)

    // 兜底：使用基础分析
    const basicAnalysis = await analyzeLogisticsStatus(orderTrack, options)

    return {
      ...basicAnalysis,
      urgency: getStatusUrgency(basicAnalysis.status),
      isFinal: isFinalStatus(basicAnalysis.status),
      recommendations: [],
      trackCount: orderTrack?.length || 0,
      analysisTimestamp: new Date().toISOString(),
      error: error.message
    }
  }
}

/**
 * 快速分析函数 - 新增便捷API
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
export async function quickAnalyzeStatus(orderTrack, options = {}) {
  return await quickAnalyze(orderTrack, options)
}

/**
 * 批量分析函数 - 新增便捷API
 * @param {Array} trackArrays - 轨迹数组的数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Array>} 分析结果数组
 */
export async function batchAnalyzeStatus(trackArrays, options = {}) {
  try {
    const analyzer = getDefaultAnalyzer()
    return await analyzer.batchAnalyze(trackArrays, options)
  } catch (error) {
    console.error('Batch analysis failed:', error)

    // 兜底：逐个分析
    const results = []
    for (const tracks of trackArrays) {
      try {
        const result = await analyzeLogisticsStatus(tracks, options)
        results.push(result)
      } catch (err) {
        results.push({
          status: LOGISTICS_STATUS.EXCEPTION,
          statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.EXCEPTION],
          confidence: 0.1,
          error: err.message,
          analysisMethod: 'batch_fallback'
        })
      }
    }
    return results
  }
}

/**
 * 获取状态信息 - 新增便捷API
 * @param {string} status - 状态值
 * @returns {Object} 状态信息
 */
export function getLogisticsStatusInfo(status) {
  return getStatusInfo(status)
}

/**
 * 创建自定义分析器 - 新增便捷API
 * @param {Object} options - 配置选项
 * @returns {LogisticsStatusAnalyzer} 分析器实例
 */
export function createLogisticsAnalyzer(options = {}) {
  return createAnalyzer(options)
}

// ============================================================================
// 以下为向后兼容性说明和迁移指南
// ============================================================================

/**
 * 迁移指南：
 *
 * 旧的使用方式：
 * ```javascript
 * import { analyzeLogisticsStatus } from './logisticsStatusAnalyzer.js'
 * const result = analyzeLogisticsStatus(orderTrack)
 * ```
 *
 * 新的推荐使用方式：
 * ```javascript
 * // 方式1：使用快速分析（推荐）
 * import { quickAnalyze } from './logistics/index.js'
 * const result = await quickAnalyze(orderTrack)
 *
 * // 方式2：使用分析器实例（高级用法）
 * import { createAnalyzer } from './logistics/index.js'
 * const analyzer = createAnalyzer({ enableAI: true, enableCache: true })
 * const result = await analyzer.analyze(orderTrack)
 *
 * // 方式3：批量分析
 * import { batchAnalyze } from './logistics/index.js'
 * const results = await batchAnalyze([track1, track2, track3])
 * ```
 *
 * 新功能特性：
 * - 🚀 大幅扩充的中文关键词库（300+ 关键词）
 * - 🤖 AI增强分析（支持TensorFlow.js、Transformers.js）
 * - ⚡ 高性能缓存和批处理
 * - 🛡️ 完善的错误处理和兜底机制
 * - 📦 模块化架构，易于维护和扩展
 * - 🎯 更高的分析准确率和置信度
 *
 * 配置选项：
 * ```javascript
 * const options = {
 *   enableAI: true,           // 启用AI分析
 *   enableCache: true,        // 启用缓存
 *   enableDebounce: false,    // 启用防抖
 *   enableErrorHandling: true // 启用错误处理
 * }
 * ```
 */

// 清理函数：释放资源
export function cleanup() {
  if (defaultAnalyzer) {
    defaultAnalyzer.cleanup()
    defaultAnalyzer = null
  }
}

// 获取性能统计
export function getPerformanceStats() {
  const analyzer = getDefaultAnalyzer()
  return analyzer.getPerformanceStats()
}
