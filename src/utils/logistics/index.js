/**
 * 物流状态分析器 - 主入口文件
 * 整合所有模块，提供统一的API接口
 */

// 导入核心模块
import {
  analyzeLogisticsStatus,
  analyzeLogisticsStatusCached,
  analyzeLogisticsStatusDebounced
} from './analyzer.js'

// 导入常量和工具
import {
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  STATUS_PRIORITY,
  STATUS_URGENCY,
  FINAL_STATUSES,
  ANALYSIS_CONFIG,
  isFinalStatus,
  getStatusUrgency,
  getStatusPriority,
  getPossibleNextStates,
  isValidTransition
} from './constants.js'

// 导入AI分析器
import {
  aiEnhancedAnalysis,
  initializeAIModels
} from './aiAnalyzer.js'

// 导入性能优化
import {
  withCache,
  withDebounce,
  batchAnalyze,
  getPerformanceStats,
  cleanup as cleanupPerformance
} from './performance.js'

// 导入错误处理
import {
  safeExecute,
  retryExecute,
  validateInput,
  globalErrorHandler,
  ERROR_TYPES,
  ERROR_SEVERITY,
  LogisticsAnalysisError
} from './errorHandler.js'

/**
 * 物流状态分析器主类
 */
export class LogisticsStatusAnalyzer {
  constructor(options = {}) {
    this.options = {
      enableAI: true,
      enableCache: true,
      enableDebounce: false,
      debounceDelay: 300,
      enableErrorHandling: true,
      ...options
    }

    this.initialized = false
    this.initPromise = null
  }

  /**
   * 初始化分析器
   * @returns {Promise} 初始化Promise
   */
  async initialize() {
    if (this.initialized) {
      return Promise.resolve()
    }

    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._doInitialize()
    return this.initPromise
  }

  /**
   * 执行初始化
   * @private
   */
  async _doInitialize() {
    try {
      // 初始化AI模型（如果启用）
      if (this.options.enableAI) {
        await initializeAIModels()
      }

      this.initialized = true
      console.log('Logistics Status Analyzer initialized successfully')
    } catch (error) {
      console.warn('Failed to initialize some components:', error)
      // 即使部分初始化失败，也标记为已初始化，使用兜底功能
      this.initialized = true
    }
  }

  /**
   * 分析物流状态
   * @param {Array} orderTrack - 物流轨迹数组
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 分析结果
   */
  async analyze(orderTrack, options = {}) {
    // 确保已初始化
    await this.initialize()

    // 合并选项
    const mergedOptions = { ...this.options, ...options }

    // 输入验证
    if (mergedOptions.enableErrorHandling) {
      const validation = validateInput(orderTrack, {
        required: true,
        type: 'object'
      })

      if (!validation.isValid) {
        throw new LogisticsAnalysisError(
          `Invalid input: ${validation.errors.join(', ')}`,
          ERROR_TYPES.VALIDATION_ERROR
        )
      }
    }

    // 选择分析函数
    let analyzeFunction = analyzeLogisticsStatus

    if (mergedOptions.enableCache && mergedOptions.enableDebounce) {
      analyzeFunction = withDebounce(analyzeLogisticsStatusCached, mergedOptions.debounceDelay)
    } else if (mergedOptions.enableCache) {
      analyzeFunction = analyzeLogisticsStatusCached
    } else if (mergedOptions.enableDebounce) {
      analyzeFunction = analyzeLogisticsStatusDebounced
    }

    // 安全执行分析
    if (mergedOptions.enableErrorHandling) {
      const safeAnalyze = safeExecute(analyzeFunction, {
        timeout: 10000,
        context: { orderTrack, options: mergedOptions }
      })
      return await safeAnalyze(orderTrack, mergedOptions)
    } else {
      return await analyzeFunction(orderTrack, mergedOptions)
    }
  }

  /**
   * 批量分析
   * @param {Array} trackArrays - 轨迹数组的数组
   * @param {Object} options - 分析选项
   * @returns {Promise<Array>} 分析结果数组
   */
  async batchAnalyze(trackArrays, options = {}) {
    await this.initialize()

    const mergedOptions = { ...this.options, ...options }

    return batchAnalyze(
      trackArrays,
      (tracks) => this.analyze(tracks, { ...mergedOptions, enableDebounce: false }),
      { batchSize: 5, batchDelay: 100 }
    )
  }

  /**
   * 获取详细分析报告
   * @param {Array} orderTrack - 物流轨迹数组
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 详细分析报告
   */
  async getDetailedAnalysis(orderTrack, options = {}) {
    const basicAnalysis = await this.analyze(orderTrack, options)

    return {
      ...basicAnalysis,
      urgency: getStatusUrgency(basicAnalysis.status),
      priority: getStatusPriority(basicAnalysis.status),
      isFinal: isFinalStatus(basicAnalysis.status),
      possibleNextStates: getPossibleNextStates(basicAnalysis.status),
      recommendations: this.generateRecommendations(basicAnalysis),
      trackCount: orderTrack?.length || 0,
      analysisTimestamp: new Date().toISOString(),
      analyzerVersion: '2.0.0'
    }
  }

  /**
   * 生成建议
   * @param {Object} analysis - 分析结果
   * @returns {Array} 建议列表
   */
  generateRecommendations(analysis) {
    const recommendations = []

    if (analysis.confidence < 0.5) {
      recommendations.push('建议联系快递公司确认具体状态')
    }

    if (analysis.status === LOGISTICS_STATUS.FAILED_DELIVERY) {
      recommendations.push('建议主动联系收件人确认地址和联系方式')
    }

    if (analysis.status === LOGISTICS_STATUS.EXCEPTION) {
      recommendations.push('建议立即联系快递公司处理异常情况')
    }

    if (analysis.status === LOGISTICS_STATUS.OUT_FOR_DELIVERY &&
        analysis.timeAnalysis?.hoursSinceUpdate > 24) {
      recommendations.push('派送时间较长，建议联系派送员确认情况')
    }

    if (analysis.status === LOGISTICS_STATUS.PENDING_PICKUP &&
        analysis.timeAnalysis?.hoursSinceUpdate > 48) {
      recommendations.push('待揽件时间较长，建议联系寄件网点')
    }

    return recommendations
  }

  /**
   * 获取性能统计
   * @returns {Object} 性能统计信息
   */
  getPerformanceStats() {
    return {
      ...getPerformanceStats(),
      errorStats: globalErrorHandler.getErrorStats(),
      initialized: this.initialized,
      options: this.options
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    cleanupPerformance()
    globalErrorHandler.clearErrorLog()
    this.initialized = false
    this.initPromise = null
  }
}

/**
 * 创建分析器实例
 * @param {Object} options - 配置选项
 * @returns {LogisticsStatusAnalyzer} 分析器实例
 */
export function createAnalyzer(options = {}) {
  return new LogisticsStatusAnalyzer(options)
}

/**
 * 快速分析函数（使用默认配置）
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
export async function quickAnalyze(orderTrack, options = {}) {
  const analyzer = createAnalyzer({ enableCache: true, enableErrorHandling: true })
  return await analyzer.analyze(orderTrack, options)
}

/**
 * 获取状态信息
 * @param {string} status - 状态值
 * @returns {Object} 状态信息
 */
export function getStatusInfo(status) {
  return {
    status,
    text: STATUS_TEXT_MAP[status],
    priority: getStatusPriority(status),
    urgency: getStatusUrgency(status),
    isFinal: isFinalStatus(status),
    possibleNextStates: getPossibleNextStates(status)
  }
}

// 导出所有常量和工具函数
export {
  // 常量
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  STATUS_PRIORITY,
  STATUS_URGENCY,
  FINAL_STATUSES,
  ANALYSIS_CONFIG,
  ERROR_TYPES,
  ERROR_SEVERITY,

  // 工具函数
  isFinalStatus,
  getStatusUrgency,
  getStatusPriority,
  getPossibleNextStates,
  isValidTransition,

  // 核心分析函数
  analyzeLogisticsStatus,
  analyzeLogisticsStatusCached,
  analyzeLogisticsStatusDebounced,

  // AI分析
  aiEnhancedAnalysis,
  initializeAIModels,

  // 性能优化
  withCache,
  withDebounce,
  batchAnalyze,
  getPerformanceStats,

  // 错误处理
  safeExecute,
  retryExecute,
  validateInput,
  LogisticsAnalysisError
}

// 默认导出分析器类
export default LogisticsStatusAnalyzer
