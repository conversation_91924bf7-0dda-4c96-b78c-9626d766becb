/**
 * 物流状态分析器 - 性能优化模块
 * 提供缓存、防抖、批处理等性能优化功能
 */

import { ANALYSIS_CONFIG } from './constants.js'

// 缓存管理器
class CacheManager {
  constructor() {
    this.cache = new Map()
    this.timestamps = new Map()
    this.maxSize = 1000
    this.expireTime = ANALYSIS_CONFIG.CACHE_EXPIRE_TIME
  }

  /**
   * 生成缓存键
   * @param {string} text - 文本内容
   * @param {Object} options - 选项
   * @returns {string} 缓存键
   */
  generateKey(text, options = {}) {
    const textStr = typeof text === 'string' ? text : JSON.stringify(text)
    const optionsStr = JSON.stringify(options)
    return `${this.hashString(textStr)}_${this.hashString(optionsStr)}`
  }

  /**
   * 简单字符串哈希函数
   * @param {string} str - 字符串
   * @returns {string} 哈希值
   */
  hashString(str) {
    let hash = 0
    if (str.length === 0) return hash.toString()

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    return Math.abs(hash).toString(36)
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} 缓存值或null
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null
    }

    const timestamp = this.timestamps.get(key)
    const now = Date.now()

    // 检查是否过期
    if (now - timestamp > this.expireTime) {
      this.delete(key)
      return null
    }

    return this.cache.get(key)
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   */
  set(key, value) {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }

    this.cache.set(key, value)
    this.timestamps.set(key, Date.now())
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.cache.delete(key)
    this.timestamps.delete(key)
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
    this.timestamps.clear()
  }

  /**
   * 驱逐最旧的缓存条目
   */
  evictOldest() {
    let oldestKey = null
    let oldestTime = Infinity

    for (const [key, timestamp] of this.timestamps) {
      if (timestamp < oldestTime) {
        oldestTime = timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      expireTime: this.expireTime
    }
  }
}

// 防抖管理器
class DebounceManager {
  constructor() {
    this.timers = new Map()
  }

  /**
   * 防抖执行函数
   * @param {string} key - 防抖键
   * @param {Function} fn - 要执行的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Promise} 执行结果Promise
   */
  debounce(key, fn, delay = 300) {
    return new Promise((resolve, reject) => {
      // 清除之前的定时器
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key).timer)
      }

      // 设置新的定时器
      const timer = setTimeout(async () => {
        try {
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.timers.delete(key)
        }
      }, delay)

      this.timers.set(key, { timer, resolve, reject })
    })
  }

  /**
   * 取消防抖
   * @param {string} key - 防抖键
   */
  cancel(key) {
    if (this.timers.has(key)) {
      const { timer, reject } = this.timers.get(key)
      clearTimeout(timer)
      reject(new Error('Debounce cancelled'))
      this.timers.delete(key)
    }
  }

  /**
   * 清除所有防抖
   */
  clear() {
    for (const [key] of this.timers) {
      this.cancel(key)
    }
  }
}

// 批处理管理器
class BatchProcessor {
  constructor() {
    this.batches = new Map()
    this.defaultBatchSize = 10
    this.defaultBatchDelay = 100
  }

  /**
   * 添加到批处理队列
   * @param {string} batchKey - 批处理键
   * @param {*} item - 要处理的项目
   * @param {Function} processor - 处理函数
   * @param {Object} options - 选项
   * @returns {Promise} 处理结果Promise
   */
  add(batchKey, item, processor, options = {}) {
    const batchSize = options.batchSize || this.defaultBatchSize
    const batchDelay = options.batchDelay || this.defaultBatchDelay

    return new Promise((resolve, reject) => {
      if (!this.batches.has(batchKey)) {
        this.batches.set(batchKey, {
          items: [],
          promises: [],
          processor,
          timer: null,
          batchSize,
          batchDelay
        })
      }

      const batch = this.batches.get(batchKey)
      batch.items.push(item)
      batch.promises.push({ resolve, reject })

      // 如果达到批处理大小，立即处理
      if (batch.items.length >= batch.batchSize) {
        this.processBatch(batchKey)
      } else {
        // 否则设置延迟处理
        if (batch.timer) {
          clearTimeout(batch.timer)
        }

        batch.timer = setTimeout(() => {
          this.processBatch(batchKey)
        }, batch.batchDelay)
      }
    })
  }

  /**
   * 处理批次
   * @param {string} batchKey - 批处理键
   */
  async processBatch(batchKey) {
    const batch = this.batches.get(batchKey)
    if (!batch || batch.items.length === 0) {
      return
    }

    const { items, promises, processor } = batch

    // 清除定时器
    if (batch.timer) {
      clearTimeout(batch.timer)
    }

    // 重置批次
    batch.items = []
    batch.promises = []
    batch.timer = null

    try {
      // 批量处理
      const results = await processor(items)

      // 分发结果
      promises.forEach((promise, index) => {
        const result = Array.isArray(results) ? results[index] : results
        promise.resolve(result)
      })
    } catch (error) {
      // 分发错误
      promises.forEach(promise => {
        promise.reject(error)
      })
    }
  }

  /**
   * 强制处理所有批次
   */
  async flushAll() {
    const batchKeys = Array.from(this.batches.keys())
    await Promise.all(batchKeys.map(key => this.processBatch(key)))
  }
}

// 内存池管理器
class ObjectPool {
  constructor(createFn, resetFn, maxSize = 100) {
    this.createFn = createFn
    this.resetFn = resetFn
    this.pool = []
    this.maxSize = maxSize
  }

  /**
   * 获取对象
   * @returns {*} 对象实例
   */
  acquire() {
    if (this.pool.length > 0) {
      return this.pool.pop()
    }
    return this.createFn()
  }

  /**
   * 释放对象
   * @param {*} obj - 要释放的对象
   */
  release(obj) {
    if (this.pool.length < this.maxSize) {
      if (this.resetFn) {
        this.resetFn(obj)
      }
      this.pool.push(obj)
    }
  }

  /**
   * 清空对象池
   */
  clear() {
    this.pool.length = 0
  }

  /**
   * 获取池状态
   * @returns {Object} 池状态
   */
  getStats() {
    return {
      available: this.pool.length,
      maxSize: this.maxSize
    }
  }
}

// 全局实例
const cacheManager = new CacheManager()
const debounceManager = new DebounceManager()
const batchProcessor = new BatchProcessor()

// 分析结果对象池
const analysisResultPool = new ObjectPool(
  () => ({
    status: null,
    statusText: null,
    confidence: 0,
    rawScore: 0,
    keywordScores: {},
    semanticAnalysis: null,
    timeAnalysis: null,
    analysisMethod: null
  }),
  (obj) => {
    obj.status = null
    obj.statusText = null
    obj.confidence = 0
    obj.rawScore = 0
    obj.keywordScores = {}
    obj.semanticAnalysis = null
    obj.timeAnalysis = null
    obj.analysisMethod = null
  }
)

/**
 * 带缓存的分析函数装饰器
 * @param {Function} analysisFunction - 分析函数
 * @returns {Function} 装饰后的函数
 */
export function withCache(analysisFunction) {
  return async function(context, options = {}) {
    const cacheKey = cacheManager.generateKey(context, options)

    // 尝试从缓存获取
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return { ...cached, fromCache: true }
    }

    // 执行分析
    const result = await analysisFunction(context, options)

    // 存入缓存
    if (result && result.confidence > 0.3) {
      cacheManager.set(cacheKey, result)
    }

    return { ...result, fromCache: false }
  }
}

/**
 * 带防抖的分析函数装饰器
 * @param {Function} analysisFunction - 分析函数
 * @param {number} delay - 防抖延迟
 * @returns {Function} 装饰后的函数
 */
export function withDebounce(analysisFunction, delay = 300) {
  return function(context, options = {}) {
    const debounceKey = cacheManager.generateKey(context, options)

    return debounceManager.debounce(
      debounceKey,
      () => analysisFunction(context, options),
      delay
    )
  }
}

/**
 * 批量分析函数
 * @param {Array} contexts - 文本数组
 * @param {Function} analysisFunction - 分析函数
 * @param {Object} options - 选项
 * @returns {Promise<Array>} 分析结果数组
 */
export function batchAnalyze(contexts, analysisFunction, options = {}) {
  return batchProcessor.add(
    'logistics_analysis',
    contexts,
    async (batchedContexts) => {
      const results = []
      for (const contextArray of batchedContexts) {
        const batchResults = await Promise.all(
          contextArray.map(context => analysisFunction(context, options))
        )
        results.push(...batchResults)
      }
      return results
    },
    options
  )
}

/**
 * 获取性能统计信息
 * @returns {Object} 性能统计
 */
export function getPerformanceStats() {
  return {
    cache: cacheManager.getStats(),
    objectPool: analysisResultPool.getStats(),
    activeBatches: batchProcessor.batches.size,
    activeDebounces: debounceManager.timers.size
  }
}

/**
 * 清理性能相关资源
 */
export function cleanup() {
  cacheManager.clear()
  debounceManager.clear()
  batchProcessor.flushAll()
  analysisResultPool.clear()
}

// 导出管理器实例
export {
  cacheManager,
  debounceManager,
  batchProcessor,
  analysisResultPool
}
